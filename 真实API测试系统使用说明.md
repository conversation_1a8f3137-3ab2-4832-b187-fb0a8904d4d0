# 真实API测试系统使用说明

## 📋 系统概述

真实API测试系统是专门为长期测试增强多算法融合投注系统而设计的工具。它可以：

- ✅ **连接真实游戏API** - 获取实时游戏数据和房间投资信息
- ✅ **模拟投注测试** - 使用增强算法进行预测，但不实际花费金钱
- ✅ **长期性能监控** - 持续监控算法性能，自动记录统计数据
- ✅ **数据分析支持** - 生成详细的测试报告和性能分析

## 🚀 快速开始

### 1. 配置API信息

编辑 `real_api_config.json` 文件，更新您的API配置：

```json
{
  "api": {
    "headers": {
      "userId": "您的用户ID",
      "token": "您的访问令牌",
      "sign": "您的签名"
    }
  }
}
```

### 2. 快速测试连接

```bash
python real_api_test_system.py test
```

### 3. 开始长期测试

```bash
python real_api_test_system.py
```

## 📊 测试模式

### 短期测试 (1-6小时)
- 适合验证API连接和基本功能
- 快速获得初步性能数据
- 用于调试和配置优化

### 长期测试 (24小时+)
- 获得更准确的算法性能评估
- 发现算法在不同时间段的表现差异
- 收集足够数据用于深度分析

## 🔧 配置说明

### API配置
```json
"api": {
  "base_url": "API基础地址",
  "headers": {
    "userId": "用户ID",
    "token": "访问令牌",
    "sign": "签名"
  }
}
```

### 测试设置
```json
"test_settings": {
  "default_duration_hours": 24,        // 默认测试时长
  "api_call_interval_seconds": 30,     // API调用间隔
  "stats_print_interval_minutes": 10,  // 统计打印间隔
  "data_save_interval_hours": 1,       // 数据保存间隔
  "max_api_errors": 100,               // 最大API错误数
  "retry_on_error": true,              // 错误时重试
  "retry_delay_seconds": 5             // 重试延迟
}
```

### 算法配置
```json
"algorithm_config": {
  "base_bet_amount": 2.0,              // 基础投注金额
  "max_bet_amount": 10.0,              // 最大投注金额
  "max_consecutive_losses": 5,         // 最大连续失败次数
  "max_daily_loss": 20000.0,           // 最大日损失
  "initial_balance": 100.0             // 初始余额
}
```

## 📈 数据输出

### 实时统计显示
系统每10分钟显示一次实时统计：
- 运行时间
- 总预测次数
- 预测准确率
- API调用统计
- 模拟收益情况

### 自动数据保存
系统每小时自动保存测试数据到JSON文件：
- `real_api_test_data_YYYYMMDD_HHMMSS.json`

### 数据内容
保存的数据包括：
- **测试信息**: 开始时间、结束时间、运行时长
- **API统计**: 调用次数、错误次数、错误率
- **预测统计**: 总预测数、正确预测数、准确率
- **算法性能**: 各算法的详细表现数据
- **历史记录**: 完整的游戏历史和预测历史

## 🎯 使用场景

### 1. 算法性能验证
```bash
# 24小时长期测试
python real_api_test_system.py
# 选择选项3 (24小时测试)
```

### 2. 参数优化测试
1. 修改 `real_api_config.json` 中的算法参数
2. 运行6小时测试观察效果
3. 对比不同参数配置的性能

### 3. 稳定性测试
```bash
# 运行更长时间的测试
python real_api_test_system.py
# 选择选项5，输入48或72小时
```

## 📊 数据分析建议

### 关键指标
- **预测准确率**: 目标 > 88%
- **API稳定性**: 错误率 < 5%
- **算法一致性**: 各算法权重变化趋势
- **时间段表现**: 不同时间的胜率差异

### 分析方法
1. **趋势分析**: 观察准确率随时间的变化
2. **算法对比**: 比较各算法的贡献度
3. **参数优化**: 基于数据调整算法参数
4. **风险评估**: 分析最大连败和回撤情况

## ⚠️ 注意事项

### 安全提醒
- ✅ 系统只进行模拟投注，不会实际花费金钱
- ✅ 所有预测都是基于历史数据的算法测试
- ⚠️ 请确保API配置信息的安全性

### 运行建议
- 🔋 长期测试建议在稳定的网络环境下运行
- 💾 定期备份测试数据文件
- 📊 建议至少运行24小时获得有效数据
- 🔄 可以随时按Ctrl+C停止测试

### 故障处理
- **API连接失败**: 检查网络连接和API配置
- **数据异常**: 查看错误日志，可能需要更新token
- **性能异常**: 对比历史数据，分析是否为正常波动

## 📞 技术支持

如果遇到问题：
1. 首先运行快速测试: `python real_api_test_system.py test`
2. 检查配置文件格式是否正确
3. 查看控制台输出的错误信息
4. 检查API token是否过期

## 🎉 预期效果

经过充分测试，增强多算法融合系统预期能够：
- 实现 **90%+** 的预测准确率
- 相比单一LCG算法提升 **1.8%+** 的性能
- 在长期运行中保持稳定的收益表现
- 通过多算法投票机制降低风险
