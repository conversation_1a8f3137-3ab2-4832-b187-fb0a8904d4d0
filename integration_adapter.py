#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔗 集成适配器 - 连接新系统与现有投注主程序
保持 lcg_betting_system_main.py 不变，通过适配器集成新的多算法系统

使用方法：
1. 在 lcg_betting_system_main.py 中导入此模块
2. 替换原有的 OptimalRandomGenerator 调用
"""

import json
import time
from typing import Dict, List, Optional, Any
from datetime import datetime

from enhanced_multi_algorithm_system import EnhancedBettingSystemV2

class LCGSystemAdapter:
    """LCG系统适配器 - 兼容原有接口"""
    
    def __init__(self, initial_balance: float = 100.0):
        # 初始化增强系统
        config = {
            'base_bet_amount': 1.0,
            'max_bet_amount': 10.0,
            'min_confidence_threshold': 0.4,  # 适中的置信度阈值
            'max_consecutive_losses': 5,
            'max_daily_loss': 30.0,
            'selective_betting': True,
            'multi_room_betting': False,  # 保持单房间投注兼容性
            'dynamic_amount_adjustment': True
        }
        
        self.enhanced_system = EnhancedBettingSystemV2(initial_balance, config)
        
        # 兼容性状态
        self.last_decision = None
        self.last_confidence = 0.5
        
        print("🔗 LCG系统适配器初始化完成")
        print("✅ 已集成多算法融合系统")
    
    def select_optimal_random_room(self, room_investment_data: Optional[Dict] = None) -> int:
        """
        兼容原有接口：选择最优随机房间
        返回推荐投注的房间号
        """
        
        # 获取当前期号（模拟）
        current_issue = int(time.time()) % 100000
        
        # 制定投注决策
        decision = self.enhanced_system.make_betting_decision(
            current_issue, room_investment_data
        )
        
        # 保存决策用于后续处理
        self.last_decision = decision
        self.last_confidence = decision['confidence']
        
        if decision['should_bet'] and decision['target_rooms']:
            # 返回第一个推荐房间
            selected_room = decision['target_rooms'][0]
            print(f"🎯 适配器推荐房间: {selected_room} (置信度: {decision['confidence']:.3f})")
            return selected_room
        else:
            # 如果不建议投注，返回随机房间但标记低置信度
            import random
            fallback_room = random.randint(1, 8)
            print(f"⚠️ 低置信度，返回随机房间: {fallback_room}")
            return fallback_room
    
    def get_betting_confidence(self) -> float:
        """获取投注置信度"""
        return self.last_confidence
    
    def should_skip_betting(self) -> bool:
        """判断是否应该跳过投注"""
        if self.last_decision:
            return not self.last_decision['should_bet']
        return False
    
    def get_recommended_bet_amount(self, base_amount: float = 1.0) -> float:
        """获取推荐投注金额"""
        if self.last_decision and self.last_decision['should_bet']:
            bet_amounts = self.last_decision['bet_amounts']
            if bet_amounts:
                return list(bet_amounts.values())[0]
        return base_amount
    
    def process_game_result(self, issue: int, actual_result: int, 
                          bet_amount: float = 0.0, won: bool = False):
        """处理游戏结果"""
        
        # 添加结果到历史
        self.enhanced_system.add_game_result(actual_result)
        
        # 如果有投注决策，处理结果
        if self.last_decision:
            # 模拟投注结果处理
            if self.last_decision['should_bet']:
                result = self.enhanced_system.process_betting_result(
                    issue, self.last_decision, actual_result
                )
                return result
        
        return None
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        summary = self.enhanced_system.get_performance_summary()
        
        return {
            'balance': summary['current_balance'],
            'total_bets': summary['total_bets'],
            'win_rate': summary['win_rate'],
            'profit': summary['total_profit'],
            'consecutive_wins': summary['consecutive_wins'],
            'consecutive_losses': summary['consecutive_losses'],
            'algorithm_weights': summary['algorithm_weights'],
            'last_confidence': self.last_confidence
        }
    
    def reset_daily_limits(self):
        """重置日限制"""
        self.enhanced_system.daily_loss = 0.0
        self.enhanced_system.session_start_time = datetime.now()
        print("🔄 日限制已重置")
    
    def update_config(self, new_config: Dict[str, Any]):
        """更新配置"""
        self.enhanced_system.config.update(new_config)
        print(f"⚙️ 配置已更新: {new_config}")
    
    def get_algorithm_performance(self) -> Dict[str, float]:
        """获取各算法性能"""
        performance = {}
        
        for algo_name, history in self.enhanced_system.multi_algorithm_voter.performance_history.items():
            if len(history) >= 5:
                recent_perf = sum(list(history)[-10:]) / min(len(history), 10)
                performance[algo_name] = recent_perf
            else:
                performance[algo_name] = 0.5
        
        return performance
    
    def export_performance_log(self, filename: str = None) -> str:
        """导出性能日志"""
        if filename is None:
            filename = f"enhanced_system_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        summary = self.enhanced_system.get_performance_summary()
        algorithm_performance = self.get_algorithm_performance()
        
        log_data = {
            'timestamp': datetime.now().isoformat(),
            'system_summary': summary,
            'algorithm_performance': algorithm_performance,
            'config': self.enhanced_system.config,
            'game_history_length': len(self.enhanced_system.game_history)
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)
            print(f"📊 性能日志已导出: {filename}")
            return filename
        except Exception as e:
            print(f"❌ 导出日志失败: {e}")
            return ""

# 全局适配器实例（单例模式）
_adapter_instance = None

def get_adapter(initial_balance: float = 100.0) -> LCGSystemAdapter:
    """获取适配器实例（单例）"""
    global _adapter_instance
    if _adapter_instance is None:
        _adapter_instance = LCGSystemAdapter(initial_balance)
    return _adapter_instance

def reset_adapter():
    """重置适配器实例"""
    global _adapter_instance
    _adapter_instance = None

# 兼容性函数 - 直接替换原有调用
def select_optimal_random_room(room_investment_data: Optional[Dict] = None) -> int:
    """兼容性函数：选择最优房间"""
    adapter = get_adapter()
    return adapter.select_optimal_random_room(room_investment_data)

def get_betting_confidence() -> float:
    """兼容性函数：获取投注置信度"""
    adapter = get_adapter()
    return adapter.get_betting_confidence()

def should_skip_betting() -> bool:
    """兼容性函数：判断是否跳过投注"""
    adapter = get_adapter()
    return adapter.should_skip_betting()

def process_game_result(issue: int, actual_result: int, bet_amount: float = 0.0, won: bool = False):
    """兼容性函数：处理游戏结果"""
    adapter = get_adapter()
    return adapter.process_game_result(issue, actual_result, bet_amount, won)

def get_system_status() -> Dict[str, Any]:
    """兼容性函数：获取系统状态"""
    adapter = get_adapter()
    return adapter.get_system_status()

# 使用示例
if __name__ == "__main__":
    print("🔗 集成适配器测试")
    
    # 创建适配器
    adapter = get_adapter(100.0)
    
    # 模拟几次调用
    for i in range(5):
        print(f"\n--- 测试 {i+1} ---")
        
        # 选择房间
        room = adapter.select_optimal_random_room()
        confidence = adapter.get_betting_confidence()
        should_skip = adapter.should_skip_betting()
        
        print(f"推荐房间: {room}")
        print(f"置信度: {confidence:.3f}")
        print(f"是否跳过: {should_skip}")
        
        # 模拟结果
        import random
        actual = random.randint(1, 8)
        won = actual == room
        
        adapter.process_game_result(140000 + i, actual, 1.0, won)
        
        time.sleep(0.5)
    
    # 显示状态
    status = adapter.get_system_status()
    print(f"\n📊 最终状态:")
    for key, value in status.items():
        print(f"   {key}: {value}")
    
    # 导出日志
    log_file = adapter.export_performance_log()
    print(f"📄 日志文件: {log_file}")
