#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 增强投注系统 v2.0 - 集成多算法融合
基于 enhanced_multi_algorithm_system.py 的完整投注系统实现

核心特性：
1. 多算法融合投票机制
2. 智能房间选择策略
3. 动态置信度评估
4. 选择性投注机制
5. 增强风险控制
"""

import json
import time
from collections import deque
from datetime import datetime
from typing import List, Dict, Optional, Any
from dataclasses import dataclass, asdict

from enhanced_multi_algorithm_system import (
    MultiAlgorithmVoter, SmartRoomSelector, VotingResult, AlgorithmResult
)

@dataclass
class BettingDecision:
    """投注决策"""
    should_bet: bool
    target_rooms: List[int]
    bet_amounts: Dict[int, float]
    confidence: float
    reason: str
    algorithm_details: Dict[str, Any]

@dataclass
class BettingResult:
    """投注结果"""
    issue: int
    decision: BettingDecision
    actual_result: int
    won: bool
    profit: float
    timestamp: datetime

class EnhancedBettingSystemV2:
    """增强投注系统 v2.0"""
    
    def __init__(self, initial_balance: float = 100.0, config: Optional[Dict] = None):
        # 基础配置
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        
        # 默认配置
        default_config = {
            'base_bet_amount': 1.0,
            'max_bet_amount': 10.0,
            'min_confidence_threshold': 0.5,  # 最低投注置信度
            'max_consecutive_losses': 5,
            'max_daily_loss': 20.0,
            'selective_betting': True,  # 启用选择性投注
            'multi_room_betting': False,  # 多房间投注
            'dynamic_amount_adjustment': True
        }
        
        self.config = {**default_config, **(config or {})}
        
        # 核心组件
        self.multi_algorithm_voter = MultiAlgorithmVoter()
        self.smart_room_selector = SmartRoomSelector()
        
        # 状态跟踪
        self.game_history = deque(maxlen=1000)
        self.betting_history = deque(maxlen=200)
        self.consecutive_losses = 0
        self.consecutive_wins = 0
        self.daily_loss = 0.0
        self.session_start_time = datetime.now()
        
        # 性能统计
        self.total_bets = 0
        self.total_wins = 0
        self.total_profit = 0.0
        
        print(f"🎯 增强投注系统 v2.0 初始化完成")
        print(f"💰 初始余额: {self.current_balance:.2f}元")
        print(f"⚙️ 选择性投注: {'启用' if self.config['selective_betting'] else '禁用'}")
        print(f"🎲 多房间投注: {'启用' if self.config['multi_room_betting'] else '禁用'}")
    
    def should_place_bet(self, confidence: float) -> tuple[bool, str]:
        """判断是否应该投注"""
        
        # 检查置信度阈值
        if self.config['selective_betting'] and confidence < self.config['min_confidence_threshold']:
            return False, f"置信度过低 ({confidence:.3f} < {self.config['min_confidence_threshold']})"
        
        # 检查连续失败限制
        if self.consecutive_losses >= self.config['max_consecutive_losses']:
            return False, f"连续失败次数过多 ({self.consecutive_losses}次)"
        
        # 检查日损失限制
        if self.daily_loss >= self.config['max_daily_loss']:
            return False, f"达到日损失限制 ({self.daily_loss:.2f}元)"
        
        # 检查余额
        min_bet = self.config['base_bet_amount']
        if self.current_balance < min_bet:
            return False, f"余额不足 ({self.current_balance:.2f}元 < {min_bet:.2f}元)"
        
        # 检查时间段风险
        current_hour = datetime.now().hour
        if 2 <= current_hour <= 5:  # 深夜高风险时段
            if confidence < 0.7:
                return False, f"深夜时段需要更高置信度 ({confidence:.3f} < 0.7)"
        
        return True, "通过所有检查"
    
    def calculate_bet_amount(self, confidence: float, room_count: int = 1) -> float:
        """计算投注金额"""
        
        base_amount = self.config['base_bet_amount']
        
        if not self.config['dynamic_amount_adjustment']:
            return base_amount
        
        # 基于置信度调整
        confidence_multiplier = 0.5 + (confidence * 1.0)  # 0.5-1.5倍
        
        # 基于连败情况调整（马丁格尔策略的保守版本）
        if self.consecutive_losses > 0:
            martingale_multiplier = min(1.5 ** self.consecutive_losses, 3.0)  # 最大3倍
        else:
            martingale_multiplier = 1.0
        
        # 基于连胜情况调整
        if self.consecutive_wins >= 3:
            win_bonus = 1 + (self.consecutive_wins - 2) * 0.1  # 每连胜1次增加10%
            win_bonus = min(win_bonus, 1.5)  # 最大1.5倍
        else:
            win_bonus = 1.0
        
        # 基于余额比例调整
        balance_ratio = self.current_balance / self.initial_balance
        balance_multiplier = min(balance_ratio, 1.0)  # 余额减少时降低投注
        
        # 综合计算
        calculated_amount = (base_amount * confidence_multiplier * 
                           martingale_multiplier * win_bonus * balance_multiplier)
        
        # 多房间投注时平均分配
        if room_count > 1:
            calculated_amount /= room_count
        
        # 限制在合理范围内
        final_amount = max(0.5, min(calculated_amount, self.config['max_bet_amount']))
        final_amount = min(final_amount, self.current_balance * 0.2)  # 不超过余额的20%
        
        return round(final_amount, 2)
    
    def make_betting_decision(self, current_issue: int, 
                            room_investment_data: Optional[Dict] = None) -> BettingDecision:
        """制定投注决策"""
        
        print(f"\n🎯 第{current_issue}期 - 增强多算法投注决策")
        print("=" * 60)
        
        # 多算法投票预测
        voting_result = self.multi_algorithm_voter.vote_for_prediction(list(self.game_history))
        
        print(f"🗳️ 算法投票结果:")
        print(f"   推荐避开房间: {voting_result.recommended_room}")
        print(f"   综合置信度: {voting_result.confidence:.3f}")
        print(f"   共识水平: {voting_result.consensus_level:.3f}")
        
        # 显示各算法详情
        for algo_name, result in voting_result.algorithm_votes.items():
            print(f"   {algo_name}: 房间{result.predicted_room} (置信度{result.confidence:.3f})")
        
        # 判断是否投注
        should_bet, bet_reason = self.should_place_bet(voting_result.confidence)
        
        if not should_bet:
            print(f"❌ 跳过投注: {bet_reason}")
            return BettingDecision(
                should_bet=False,
                target_rooms=[],
                bet_amounts={},
                confidence=voting_result.confidence,
                reason=bet_reason,
                algorithm_details=asdict(voting_result)
            )
        
        # 智能房间选择
        target_rooms = self.smart_room_selector.select_optimal_rooms(
            avoid_room=voting_result.recommended_room,
            confidence=voting_result.confidence,
            room_investment_data=room_investment_data
        )
        
        # 计算投注金额
        room_count = len(target_rooms) if self.config['multi_room_betting'] else 1
        if not self.config['multi_room_betting']:
            target_rooms = target_rooms[:1]  # 只选择第一个房间
        
        bet_amount = self.calculate_bet_amount(voting_result.confidence, room_count)
        bet_amounts = {room: bet_amount for room in target_rooms}
        
        print(f"✅ 投注决策:")
        print(f"   目标房间: {target_rooms}")
        print(f"   投注金额: {bet_amount:.2f}元/房间")
        print(f"   总投注: {sum(bet_amounts.values()):.2f}元")
        print(f"   决策理由: {bet_reason}")
        
        return BettingDecision(
            should_bet=True,
            target_rooms=target_rooms,
            bet_amounts=bet_amounts,
            confidence=voting_result.confidence,
            reason=bet_reason,
            algorithm_details=asdict(voting_result)
        )
    
    def process_betting_result(self, issue: int, decision: BettingDecision, 
                             actual_result: int) -> BettingResult:
        """处理投注结果"""
        
        if not decision.should_bet:
            # 未投注的情况
            result = BettingResult(
                issue=issue,
                decision=decision,
                actual_result=actual_result,
                won=False,
                profit=0.0,
                timestamp=datetime.now()
            )
            
            # 更新算法性能（即使未投注也要更新）
            voting_result = VotingResult(
                recommended_room=decision.algorithm_details.get('recommended_room', 1),
                confidence=decision.confidence,
                algorithm_votes={},  # 简化处理
                consensus_level=decision.algorithm_details.get('consensus_level', 0)
            )
            
            # 这里需要重构算法结果
            for algo_name in ['LCG', 'Frequency', 'Pattern']:
                algo_data = decision.algorithm_details.get('algorithm_votes', {}).get(algo_name, {})
                if algo_data:
                    voting_result.algorithm_votes[algo_name] = AlgorithmResult(
                        algorithm_name=algo_data.get('algorithm_name', algo_name),
                        predicted_room=algo_data.get('predicted_room', 1),
                        confidence=algo_data.get('confidence', 0.5),
                        avoid_rooms=algo_data.get('avoid_rooms', []),
                        metadata=algo_data.get('metadata', {})
                    )
            
            self.multi_algorithm_voter.update_algorithm_performance(actual_result, voting_result)
            
            print(f"📊 第{issue}期结果: 未投注 (开奖房间{actual_result})")
            return result
        
        # 计算投注结果
        won = actual_result in decision.target_rooms
        total_bet = sum(decision.bet_amounts.values())
        
        if won:
            # 胜利：获得10%收益
            profit = total_bet * 0.1
            self.consecutive_wins += 1
            self.consecutive_losses = 0
        else:
            # 失败：损失全部投注金额
            profit = -total_bet
            self.consecutive_losses += 1
            self.consecutive_wins = 0
            self.daily_loss += total_bet
        
        # 更新余额和统计
        self.current_balance += profit
        self.total_bets += 1
        if won:
            self.total_wins += 1
        self.total_profit += profit
        
        # 创建结果对象
        result = BettingResult(
            issue=issue,
            decision=decision,
            actual_result=actual_result,
            won=won,
            profit=profit,
            timestamp=datetime.now()
        )
        
        # 更新历史记录
        self.betting_history.append(result)
        
        # 更新算法性能
        voting_result = VotingResult(
            recommended_room=decision.algorithm_details.get('recommended_room', 1),
            confidence=decision.confidence,
            algorithm_votes={},
            consensus_level=decision.algorithm_details.get('consensus_level', 0)
        )
        
        # 重构算法结果用于性能更新
        for algo_name in ['LCG', 'Frequency', 'Pattern']:
            algo_data = decision.algorithm_details.get('algorithm_votes', {}).get(algo_name, {})
            if algo_data:
                voting_result.algorithm_votes[algo_name] = AlgorithmResult(
                    algorithm_name=algo_data.get('algorithm_name', algo_name),
                    predicted_room=algo_data.get('predicted_room', 1),
                    confidence=algo_data.get('confidence', 0.5),
                    avoid_rooms=algo_data.get('avoid_rooms', []),
                    metadata=algo_data.get('metadata', {})
                )
        
        self.multi_algorithm_voter.update_algorithm_performance(actual_result, voting_result)
        
        # 更新房间性能
        for room in decision.target_rooms:
            self.smart_room_selector.update_room_performance(room, won)
        
        # 显示结果
        status = "🎉 获胜" if won else "❌ 失败"
        print(f"📊 第{issue}期结果: {status}")
        print(f"   开奖房间: {actual_result}")
        print(f"   投注房间: {decision.target_rooms}")
        print(f"   盈亏: {profit:+.2f}元")
        print(f"   当前余额: {self.current_balance:.2f}元")
        print(f"   连胜/连败: {self.consecutive_wins}/{self.consecutive_losses}")
        
        return result
