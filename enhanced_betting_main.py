#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 增强多算法投注系统主程序 v1.0
基于 enhanced_multi_algorithm_system.py 的完整投注系统

使用方法：
python enhanced_betting_main.py

特性：
1. 多算法融合投票机制
2. 智能房间选择策略  
3. 选择性投注机制
4. 动态参数调优
5. 实时性能监控
"""

import json
import time
import random
from datetime import datetime
from typing import Dict, List, Optional

from enhanced_multi_algorithm_system import EnhancedBettingSystemV2

def load_game_history(filename: str = "game_history.json") -> List[int]:
    """加载游戏历史数据"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
            history = data.get('history', [])
            print(f"📚 加载历史数据: {len(history)}期")
            return history
    except FileNotFoundError:
        print(f"⚠️ 历史数据文件 {filename} 不存在，使用空历史")
        return []
    except Exception as e:
        print(f"❌ 加载历史数据失败: {e}")
        return []

def save_game_history(history: List[int], filename: str = "game_history.json"):
    """保存游戏历史数据"""
    try:
        data = {"history": history}
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"💾 历史数据已保存: {len(history)}期")
    except Exception as e:
        print(f"❌ 保存历史数据失败: {e}")

def simulate_game_result() -> int:
    """模拟游戏结果（1-8房间随机）"""
    return random.randint(1, 8)

def get_room_investment_data() -> Dict:
    """获取房间投入数据（模拟）"""
    # 这里可以接入真实的房间投入数据API
    # 现在使用模拟数据
    rooms_data = {}
    for i in range(1, 9):
        rooms_data[f"房间{i}"] = {
            '投入金额': random.randint(50, 500),
            '房间人数': random.randint(5, 30),
            '投入道具': random.randint(1000, 8000)
        }
    return rooms_data

def display_performance_report(system: EnhancedBettingSystemV2):
    """显示性能报告"""
    summary = system.get_performance_summary()
    
    print(f"\n📊 系统性能报告")
    print("=" * 50)
    print(f"💰 当前余额: {summary['current_balance']:.2f}元")
    print(f"📈 投资回报率: {summary['roi']:+.2f}%")
    print(f"🎯 总投注次数: {summary['total_bets']}")
    print(f"🏆 获胜次数: {summary['total_wins']}")
    print(f"📊 胜率: {summary['win_rate']:.1%}")
    print(f"💵 总盈亏: {summary['total_profit']:+.2f}元")
    print(f"🔥 连胜: {summary['consecutive_wins']} | 连败: {summary['consecutive_losses']}")
    print(f"⏱️ 运行时长: {summary['session_duration']}")
    
    print(f"\n🤖 算法权重分布:")
    for algo, weight in summary['algorithm_weights'].items():
        print(f"   {algo}: {weight:.3f}")
    
    print(f"\n📈 最近算法性能:")
    for algo, perf in summary['recent_algorithm_performance'].items():
        print(f"   {algo}: {perf:.1%}")

def interactive_mode():
    """交互模式"""
    print(f"\n🎮 进入交互模式")
    print("命令说明:")
    print("  bet <期号> - 执行单次投注")
    print("  auto <次数> - 自动投注指定次数")
    print("  report - 显示性能报告")
    print("  config - 显示当前配置")
    print("  quit - 退出系统")
    
    # 初始化系统
    config = {
        'base_bet_amount': 1.0,
        'max_bet_amount': 8.0,
        'min_confidence_threshold': 0.4,  # 降低阈值以便测试
        'selective_betting': True,
        'multi_room_betting': False,
        'dynamic_amount_adjustment': True
    }
    
    system = EnhancedBettingSystemV2(initial_balance=100.0, config=config)
    
    # 加载历史数据
    history = load_game_history()
    for result in history:
        system.add_game_result(result)
    
    current_issue = len(history) + 140000  # 假设起始期号
    
    while True:
        try:
            command = input(f"\n[期号{current_issue}] 请输入命令: ").strip().lower()
            
            if command == "quit":
                print("👋 系统退出")
                break
            
            elif command == "report":
                display_performance_report(system)
            
            elif command == "config":
                print(f"\n⚙️ 当前配置:")
                for key, value in system.config.items():
                    print(f"   {key}: {value}")
            
            elif command.startswith("bet"):
                parts = command.split()
                if len(parts) > 1:
                    try:
                        issue = int(parts[1])
                    except ValueError:
                        issue = current_issue
                else:
                    issue = current_issue
                
                # 获取房间投入数据
                room_data = get_room_investment_data()
                
                # 制定投注决策
                decision = system.make_betting_decision(issue, room_data)
                
                # 模拟开奖
                actual_result = simulate_game_result()
                
                # 处理结果
                result = system.process_betting_result(issue, decision, actual_result)
                
                # 更新历史
                system.add_game_result(actual_result)
                history.append(actual_result)
                
                current_issue += 1
            
            elif command.startswith("auto"):
                parts = command.split()
                try:
                    count = int(parts[1]) if len(parts) > 1 else 5
                except ValueError:
                    count = 5
                
                print(f"🤖 自动投注 {count} 期")
                
                for i in range(count):
                    print(f"\n--- 自动投注 {i+1}/{count} ---")
                    
                    # 获取房间投入数据
                    room_data = get_room_investment_data()
                    
                    # 制定投注决策
                    decision = system.make_betting_decision(current_issue, room_data)
                    
                    # 模拟开奖
                    actual_result = simulate_game_result()
                    
                    # 处理结果
                    result = system.process_betting_result(current_issue, decision, actual_result)
                    
                    # 更新历史
                    system.add_game_result(actual_result)
                    history.append(actual_result)
                    
                    current_issue += 1
                    
                    # 短暂延迟
                    time.sleep(0.5)
                
                # 显示汇总报告
                display_performance_report(system)
            
            else:
                print("❓ 未知命令，请重新输入")
        
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，系统退出")
            break
        except Exception as e:
            print(f"❌ 执行命令时出错: {e}")
    
    # 保存历史数据
    save_game_history(history)

def demo_mode():
    """演示模式"""
    print(f"\n🎬 演示模式 - 自动运行20期")
    
    # 初始化系统
    config = {
        'base_bet_amount': 1.0,
        'max_bet_amount': 5.0,
        'min_confidence_threshold': 0.3,
        'selective_betting': True,
        'multi_room_betting': False,
        'dynamic_amount_adjustment': True
    }
    
    system = EnhancedBettingSystemV2(initial_balance=50.0, config=config)
    
    # 加载历史数据
    history = load_game_history()
    for result in history:
        system.add_game_result(result)
    
    current_issue = len(history) + 140000
    demo_count = 20
    
    print(f"🚀 开始演示，共{demo_count}期")
    
    for i in range(demo_count):
        print(f"\n{'='*60}")
        print(f"🎯 演示进度: {i+1}/{demo_count}")
        
        # 获取房间投入数据
        room_data = get_room_investment_data()
        
        # 制定投注决策
        decision = system.make_betting_decision(current_issue, room_data)
        
        # 模拟开奖
        actual_result = simulate_game_result()
        
        # 处理结果
        result = system.process_betting_result(current_issue, decision, actual_result)
        
        # 更新历史
        system.add_game_result(actual_result)
        history.append(actual_result)
        
        current_issue += 1
        
        # 每5期显示一次中间报告
        if (i + 1) % 5 == 0:
            print(f"\n📊 中间报告 ({i+1}/{demo_count}期):")
            summary = system.get_performance_summary()
            print(f"   余额: {summary['current_balance']:.2f}元")
            print(f"   胜率: {summary['win_rate']:.1%}")
            print(f"   盈亏: {summary['total_profit']:+.2f}元")
        
        time.sleep(0.3)  # 演示延迟
    
    # 最终报告
    print(f"\n🎉 演示完成！")
    display_performance_report(system)
    
    # 保存历史数据
    save_game_history(history)

def main():
    """主程序"""
    print("🎯 增强多算法投注系统 v1.0")
    print("=" * 50)
    print("基于多算法融合的智能投注系统")
    print("特性: LCG + 频率统计 + 模式检测 + 智能房间选择")
    
    print(f"\n🎮 选择运行模式:")
    print("1. 交互模式 - 手动控制投注")
    print("2. 演示模式 - 自动运行20期展示")
    
    while True:
        try:
            choice = input("\n请选择模式 (1-2): ").strip()
            
            if choice == "1":
                interactive_mode()
                break
            elif choice == "2":
                demo_mode()
                break
            else:
                print("❓ 请输入有效选项 (1-2)")
        
        except KeyboardInterrupt:
            print("\n\n👋 用户取消，程序退出")
            break
        except Exception as e:
            print(f"❌ 程序出错: {e}")
            break

if __name__ == "__main__":
    main()
