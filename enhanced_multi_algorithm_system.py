#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 增强多算法融合投注系统 v1.0
实现阶段1优化：多算法融合 + 动态参数调优 + 智能房间选择

核心特性：
1. 多算法投票机制（LCG + 频率统计 + 周期检测）
2. LCG参数动态调优
3. 智能房间选择策略优化
4. 置信度评估系统
"""

import json
import random
import time
from collections import Counter, deque
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional, Any
import numpy as np
from dataclasses import dataclass
import math

@dataclass
class AlgorithmResult:
    """算法预测结果"""
    algorithm_name: str
    predicted_room: int
    confidence: float
    avoid_rooms: List[int]
    metadata: Dict[str, Any]

@dataclass
class VotingResult:
    """投票结果"""
    recommended_room: int
    confidence: float
    algorithm_votes: Dict[str, AlgorithmResult]
    consensus_level: float

class LCGPredictor:
    """增强LCG预测器 - 支持动态参数调优"""
    
    def __init__(self):
        # 多组LCG参数配置
        self.lcg_configs = [
            {'name': 'standard', 'a': 1664525, 'c': 1013904223, 'm': 2**32, 'weight': 1.0},
            {'name': 'alternative1', 'a': 1103515245, 'c': 12345, 'm': 2**31, 'weight': 0.8},
            {'name': 'alternative2', 'a': 214013, 'c': 2531011, 'm': 2**32, 'weight': 0.6},
            {'name': 'small_param', 'a': 17, 'c': 5, 'm': 256, 'weight': 0.4},
        ]
        
        # 动态种子管理
        self.seeds = {}
        self.performance_history = {}
        self.last_update = datetime.now()
        
        # 初始化种子
        for config in self.lcg_configs:
            self.seeds[config['name']] = int(time.time()) % config['m']
            self.performance_history[config['name']] = deque(maxlen=50)
    
    def generate_sequence(self, config_name: str, length: int) -> List[int]:
        """生成LCG序列"""
        config = next(c for c in self.lcg_configs if c['name'] == config_name)
        
        sequence = []
        x = self.seeds[config_name]
        
        for _ in range(length):
            x = (config['a'] * x + config['c']) % config['m']
            mapped = (x % 8) + 1
            sequence.append(mapped)
        
        # 更新种子状态
        self.seeds[config_name] = x
        return sequence
    
    def predict_next_room(self, history: List[int]) -> AlgorithmResult:
        """预测下一个房间"""
        if len(history) < 10:
            # 历史数据不足，使用默认预测
            predicted = random.randint(1, 8)
            return AlgorithmResult(
                algorithm_name="LCG_insufficient_data",
                predicted_room=predicted,
                confidence=0.3,
                avoid_rooms=[predicted],
                metadata={'reason': 'insufficient_history'}
            )
        
        # 多配置投票
        config_predictions = {}
        
        for config in self.lcg_configs:
            try:
                # 尝试匹配历史序列
                best_match_rate = 0
                best_prediction = 1
                
                # 测试不同的起始位置
                for start_pos in range(min(10, len(history))):
                    test_history = history[start_pos:]
                    if len(test_history) < 5:
                        continue
                    
                    # 生成测试序列
                    test_seed = self.seeds[config['name']]
                    generated = []
                    x = test_seed
                    
                    for _ in range(len(test_history) + 1):
                        x = (config['a'] * x + config['c']) % config['m']
                        mapped = (x % 8) + 1
                        generated.append(mapped)
                    
                    # 计算匹配率
                    matches = sum(1 for i in range(len(test_history)) 
                                if generated[i] == test_history[i])
                    match_rate = matches / len(test_history)
                    
                    if match_rate > best_match_rate:
                        best_match_rate = match_rate
                        best_prediction = generated[-1]  # 下一个预测值
                
                config_predictions[config['name']] = {
                    'prediction': best_prediction,
                    'match_rate': best_match_rate,
                    'weight': config['weight']
                }
                
            except Exception as e:
                print(f"LCG配置 {config['name']} 预测失败: {e}")
                config_predictions[config['name']] = {
                    'prediction': random.randint(1, 8),
                    'match_rate': 0.0,
                    'weight': 0.1
                }
        
        # 加权投票选择最佳预测
        weighted_votes = {}
        total_weight = 0
        
        for config_name, pred_data in config_predictions.items():
            prediction = pred_data['prediction']
            weight = pred_data['weight'] * (1 + pred_data['match_rate'])
            
            if prediction not in weighted_votes:
                weighted_votes[prediction] = 0
            weighted_votes[prediction] += weight
            total_weight += weight
        
        # 选择得票最高的房间
        if weighted_votes:
            best_room = max(weighted_votes.keys(), key=lambda x: weighted_votes[x])
            confidence = weighted_votes[best_room] / total_weight if total_weight > 0 else 0.5
        else:
            best_room = random.randint(1, 8)
            confidence = 0.3
        
        return AlgorithmResult(
            algorithm_name="LCG_multi_config",
            predicted_room=best_room,
            confidence=min(confidence, 0.95),  # 限制最大置信度
            avoid_rooms=[best_room],
            metadata={
                'config_predictions': config_predictions,
                'weighted_votes': weighted_votes
            }
        )
    
    def update_performance(self, config_name: str, actual_result: int, predicted: int):
        """更新算法性能"""
        success = 1 if actual_result != predicted else 0  # 避开成功为1
        self.performance_history[config_name].append(success)
        
        # 动态调整权重
        if len(self.performance_history[config_name]) >= 10:
            recent_performance = sum(list(self.performance_history[config_name])[-10:]) / 10
            
            # 找到对应配置并更新权重
            for config in self.lcg_configs:
                if config['name'] == config_name:
                    # 基于性能调整权重
                    config['weight'] = max(0.1, min(1.5, recent_performance * 1.2))
                    break

class FrequencyPredictor:
    """频率统计预测器"""
    
    def __init__(self, window_size: int = 50):
        self.window_size = window_size
        self.frequency_weights = {
            'recent': 0.4,      # 最近期权重
            'medium': 0.3,      # 中期权重  
            'long': 0.2,        # 长期权重
            'trend': 0.1        # 趋势权重
        }
    
    def predict_next_room(self, history: List[int]) -> AlgorithmResult:
        """基于频率统计预测下一个房间"""
        if len(history) < 10:
            return AlgorithmResult(
                algorithm_name="Frequency_insufficient_data",
                predicted_room=random.randint(1, 8),
                confidence=0.2,
                avoid_rooms=[],
                metadata={'reason': 'insufficient_history'}
            )
        
        # 多时间窗口频率分析
        analysis_windows = {
            'recent': history[-10:] if len(history) >= 10 else history,
            'medium': history[-25:] if len(history) >= 25 else history,
            'long': history[-50:] if len(history) >= 50 else history
        }
        
        room_scores = {i: 0.0 for i in range(1, 9)}
        
        # 计算各时间窗口的频率得分
        for window_name, window_data in analysis_windows.items():
            if not window_data:
                continue
                
            counter = Counter(window_data)
            total_count = len(window_data)
            
            # 计算反向频率得分（出现频率低的房间得分高）
            for room in range(1, 9):
                frequency = counter.get(room, 0) / total_count
                # 反向得分：频率越低，得分越高
                reverse_score = (1 - frequency) * self.frequency_weights.get(window_name, 0.1)
                room_scores[room] += reverse_score
        
        # 趋势分析
        if len(history) >= 6:
            recent_trend = history[-6:]
            trend_counter = Counter(recent_trend)
            
            # 对连续出现的房间给予负分
            for room, count in trend_counter.items():
                if count >= 2:  # 最近6期出现2次以上
                    room_scores[room] -= 0.1 * count * self.frequency_weights['trend']
        
        # 选择得分最高的房间作为避开目标
        if room_scores:
            avoid_room = max(room_scores.keys(), key=lambda x: room_scores[x])
            confidence = min(room_scores[avoid_room] / max(room_scores.values()), 0.9)
        else:
            avoid_room = random.randint(1, 8)
            confidence = 0.3
        
        return AlgorithmResult(
            algorithm_name="Frequency_Analysis",
            predicted_room=avoid_room,
            confidence=confidence,
            avoid_rooms=[avoid_room],
            metadata={
                'room_scores': room_scores,
                'analysis_windows': {k: len(v) for k, v in analysis_windows.items()}
            }
        )

class PatternPredictor:
    """模式/周期检测预测器"""
    
    def __init__(self):
        self.max_pattern_length = 10
        self.min_pattern_occurrences = 2
    
    def find_patterns(self, sequence: List[int]) -> Dict[str, Any]:
        """寻找重复模式"""
        patterns = {}
        
        for length in range(2, min(self.max_pattern_length + 1, len(sequence) // 2)):
            for start in range(len(sequence) - length):
                pattern = tuple(sequence[start:start + length])
                
                if pattern not in patterns:
                    patterns[pattern] = []
                
                # 寻找该模式的其他出现位置
                for i in range(start + length, len(sequence) - length + 1):
                    if tuple(sequence[i:i + length]) == pattern:
                        patterns[pattern].append(i)
        
        # 过滤出现次数足够的模式
        significant_patterns = {
            pattern: positions for pattern, positions in patterns.items()
            if len(positions) >= self.min_pattern_occurrences - 1
        }
        
        return significant_patterns
    
    def predict_next_room(self, history: List[int]) -> AlgorithmResult:
        """基于模式预测下一个房间"""
        if len(history) < 10:
            return AlgorithmResult(
                algorithm_name="Pattern_insufficient_data",
                predicted_room=random.randint(1, 8),
                confidence=0.2,
                avoid_rooms=[],
                metadata={'reason': 'insufficient_history'}
            )
        
        patterns = self.find_patterns(history)
        
        if not patterns:
            return AlgorithmResult(
                algorithm_name="Pattern_no_patterns",
                predicted_room=random.randint(1, 8),
                confidence=0.3,
                avoid_rooms=[],
                metadata={'patterns_found': 0}
            )
        
        # 寻找最近匹配的模式
        predictions = []
        
        for pattern, positions in patterns.items():
            pattern_length = len(pattern)
            
            # 检查序列末尾是否匹配该模式的开始部分
            for match_length in range(1, pattern_length):
                if len(history) >= match_length:
                    recent_suffix = tuple(history[-match_length:])
                    pattern_prefix = pattern[:match_length]
                    
                    if recent_suffix == pattern_prefix:
                        # 找到匹配，预测下一个值
                        if match_length < pattern_length:
                            next_value = pattern[match_length]
                            confidence = (len(positions) + 1) / 10  # 基于模式出现频率
                            confidence *= (match_length / pattern_length)  # 基于匹配长度
                            
                            predictions.append({
                                'room': next_value,
                                'confidence': min(confidence, 0.8),
                                'pattern': pattern,
                                'match_length': match_length
                            })
        
        if predictions:
            # 选择置信度最高的预测
            best_prediction = max(predictions, key=lambda x: x['confidence'])
            
            return AlgorithmResult(
                algorithm_name="Pattern_Detection",
                predicted_room=best_prediction['room'],
                confidence=best_prediction['confidence'],
                avoid_rooms=[best_prediction['room']],
                metadata={
                    'patterns_found': len(patterns),
                    'predictions': len(predictions),
                    'best_pattern': best_prediction['pattern'],
                    'match_length': best_prediction['match_length']
                }
            )
        
        return AlgorithmResult(
            algorithm_name="Pattern_no_match",
            predicted_room=random.randint(1, 8),
            confidence=0.3,
            avoid_rooms=[],
            metadata={'patterns_found': len(patterns), 'predictions': 0}
        )

class MultiAlgorithmVoter:
    """多算法投票系统"""

    def __init__(self):
        self.lcg_predictor = LCGPredictor()
        self.frequency_predictor = FrequencyPredictor()
        self.pattern_predictor = PatternPredictor()

        # 算法权重（可动态调整）
        self.algorithm_weights = {
            'LCG': 0.5,
            'Frequency': 0.3,
            'Pattern': 0.2
        }

        # 性能历史
        self.performance_history = {
            'LCG': deque(maxlen=100),
            'Frequency': deque(maxlen=100),
            'Pattern': deque(maxlen=100)
        }

    def vote_for_prediction(self, history: List[int]) -> VotingResult:
        """多算法投票预测"""

        # 获取各算法预测结果
        algorithm_results = {}

        try:
            lcg_result = self.lcg_predictor.predict_next_room(history)
            algorithm_results['LCG'] = lcg_result
        except Exception as e:
            print(f"LCG预测失败: {e}")
            algorithm_results['LCG'] = AlgorithmResult(
                "LCG_error", random.randint(1, 8), 0.1, [], {'error': str(e)}
            )

        try:
            freq_result = self.frequency_predictor.predict_next_room(history)
            algorithm_results['Frequency'] = freq_result
        except Exception as e:
            print(f"频率预测失败: {e}")
            algorithm_results['Frequency'] = AlgorithmResult(
                "Frequency_error", random.randint(1, 8), 0.1, [], {'error': str(e)}
            )

        try:
            pattern_result = self.pattern_predictor.predict_next_room(history)
            algorithm_results['Pattern'] = pattern_result
        except Exception as e:
            print(f"模式预测失败: {e}")
            algorithm_results['Pattern'] = AlgorithmResult(
                "Pattern_error", random.randint(1, 8), 0.1, [], {'error': str(e)}
            )

        # 投票计算
        room_votes = {i: 0.0 for i in range(1, 9)}
        total_weight = 0

        for algo_name, result in algorithm_results.items():
            weight = self.algorithm_weights.get(algo_name, 0.1)
            confidence_weight = weight * result.confidence

            # 对预测房间投票（避开票）
            room_votes[result.predicted_room] += confidence_weight
            total_weight += confidence_weight

        # 选择得票最高的房间作为避开目标
        if total_weight > 0:
            avoid_room = max(room_votes.keys(), key=lambda x: room_votes[x])
            consensus_confidence = room_votes[avoid_room] / total_weight
        else:
            avoid_room = random.randint(1, 8)
            consensus_confidence = 0.3

        # 计算共识水平
        vote_values = list(room_votes.values())
        if len(vote_values) > 1:
            max_vote = max(vote_values)
            second_max = sorted(vote_values, reverse=True)[1]
            consensus_level = (max_vote - second_max) / max_vote if max_vote > 0 else 0
        else:
            consensus_level = 0.5

        return VotingResult(
            recommended_room=avoid_room,
            confidence=min(consensus_confidence, 0.95),
            algorithm_votes=algorithm_results,
            consensus_level=consensus_level
        )

    def update_algorithm_performance(self, actual_result: int, voting_result: VotingResult):
        """更新算法性能"""

        for algo_name, result in voting_result.algorithm_votes.items():
            # 计算避开成功率
            success = 1 if actual_result != result.predicted_room else 0
            self.performance_history[algo_name].append(success)

            # 动态调整权重
            if len(self.performance_history[algo_name]) >= 20:
                recent_performance = sum(list(self.performance_history[algo_name])[-20:]) / 20

                # 基于性能调整权重
                base_weight = {
                    'LCG': 0.5,
                    'Frequency': 0.3,
                    'Pattern': 0.2
                }.get(algo_name, 0.1)

                # 性能好的算法增加权重，性能差的减少权重
                performance_multiplier = 0.5 + (recent_performance * 1.0)  # 0.5-1.5倍
                self.algorithm_weights[algo_name] = base_weight * performance_multiplier

                # 确保权重在合理范围内
                self.algorithm_weights[algo_name] = max(0.05, min(0.8, self.algorithm_weights[algo_name]))

        # 归一化权重
        total_weight = sum(self.algorithm_weights.values())
        if total_weight > 0:
            for algo_name in self.algorithm_weights:
                self.algorithm_weights[algo_name] /= total_weight

class SmartRoomSelector:
    """智能房间选择器"""

    def __init__(self):
        self.room_performance_history = {i: deque(maxlen=50) for i in range(1, 9)}
        self.time_based_patterns = {}
        self.investment_based_selection = True

    def select_optimal_rooms(self, avoid_room: int, confidence: float,
                           room_investment_data: Optional[Dict] = None) -> List[int]:
        """选择最优投注房间"""

        available_rooms = [i for i in range(1, 9) if i != avoid_room]

        if confidence < 0.4:
            # 低置信度：随机选择
            return [random.choice(available_rooms)]

        # 基于投入金额数据选择
        if room_investment_data and self.investment_based_selection:
            return self._select_by_investment_analysis(available_rooms, room_investment_data)

        # 基于历史性能选择
        return self._select_by_performance(available_rooms, confidence)

    def _select_by_investment_analysis(self, available_rooms: List[int],
                                     investment_data: Dict) -> List[int]:
        """基于投入金额分析选择房间"""

        room_scores = {}

        for room in available_rooms:
            room_key = f"房间{room}"
            if room_key in investment_data:
                data = investment_data[room_key]

                # 计算综合得分
                investment_amount = data.get('投入金额', 0)
                player_count = data.get('房间人数', 1)
                prop_amount = data.get('投入道具', 0)

                # 投入金额越少，人数越少，得分越高
                investment_score = 1000 / max(investment_amount, 1)
                player_score = 50 / max(player_count, 1)
                prop_score = 1000 / max(prop_amount, 1)

                room_scores[room] = investment_score + player_score + prop_score
            else:
                room_scores[room] = 100  # 默认得分

        # 选择得分最高的房间
        if room_scores:
            best_rooms = sorted(room_scores.keys(), key=lambda x: room_scores[x], reverse=True)
            return best_rooms[:3]  # 返回前3个最佳房间

        return available_rooms[:3]

    def _select_by_performance(self, available_rooms: List[int], confidence: float) -> List[int]:
        """基于历史性能选择房间"""

        room_scores = {}

        for room in available_rooms:
            if len(self.room_performance_history[room]) >= 5:
                # 计算该房间的历史胜率
                recent_wins = sum(list(self.room_performance_history[room])[-10:])
                recent_total = min(len(self.room_performance_history[room]), 10)
                win_rate = recent_wins / recent_total if recent_total > 0 else 0.5

                room_scores[room] = win_rate
            else:
                room_scores[room] = 0.5  # 默认胜率

        # 根据置信度决定选择策略
        if confidence >= 0.8:
            # 高置信度：选择胜率最高的房间
            sorted_rooms = sorted(room_scores.keys(), key=lambda x: room_scores[x], reverse=True)
            return sorted_rooms[:2]
        elif confidence >= 0.6:
            # 中等置信度：选择胜率较高的房间
            sorted_rooms = sorted(room_scores.keys(), key=lambda x: room_scores[x], reverse=True)
            return sorted_rooms[:3]
        else:
            # 低置信度：随机选择
            return random.sample(available_rooms, min(3, len(available_rooms)))

    def update_room_performance(self, room: int, won: bool):
        """更新房间性能"""
        self.room_performance_history[room].append(1 if won else 0)

    def get_time_based_recommendation(self) -> Dict[str, Any]:
        """基于时间的推荐"""
        current_hour = datetime.now().hour

        # 不同时间段的房间偏好（基于历史数据分析）
        time_preferences = {
            'morning': [2, 4, 6],      # 早上 6-12
            'afternoon': [1, 3, 7],    # 下午 12-18
            'evening': [5, 8, 1],      # 晚上 18-24
            'night': [3, 5, 7]         # 深夜 0-6
        }

        if 6 <= current_hour < 12:
            period = 'morning'
        elif 12 <= current_hour < 18:
            period = 'afternoon'
        elif 18 <= current_hour < 24:
            period = 'evening'
        else:
            period = 'night'

        return {
            'period': period,
            'preferred_rooms': time_preferences[period],
            'hour': current_hour
        }

class EnhancedBettingSystemV2:
    """增强投注系统 v2.0 - 集成多算法融合"""

    def __init__(self, initial_balance: float = 100.0, config: Optional[Dict] = None):
        # 基础配置
        self.initial_balance = initial_balance
        self.current_balance = initial_balance

        # 默认配置
        default_config = {
            'base_bet_amount': 1.0,
            'max_bet_amount': 10.0,
            'min_confidence_threshold': 0.5,  # 最低投注置信度
            'max_consecutive_losses': 5,
            'max_daily_loss': 20.0,
            'selective_betting': True,  # 启用选择性投注
            'multi_room_betting': False,  # 多房间投注
            'dynamic_amount_adjustment': True
        }

        self.config = {**default_config, **(config or {})}

        # 核心组件
        self.multi_algorithm_voter = MultiAlgorithmVoter()
        self.smart_room_selector = SmartRoomSelector()

        # 状态跟踪
        self.game_history = deque(maxlen=1000)
        self.betting_history = deque(maxlen=200)
        self.consecutive_losses = 0
        self.consecutive_wins = 0
        self.daily_loss = 0.0
        self.session_start_time = datetime.now()

        # 性能统计
        self.total_bets = 0
        self.total_wins = 0
        self.total_profit = 0.0

        print(f"🎯 增强投注系统 v2.0 初始化完成")
        print(f"💰 初始余额: {self.current_balance:.2f}元")
        print(f"⚙️ 选择性投注: {'启用' if self.config['selective_betting'] else '禁用'}")
        print(f"🎲 多房间投注: {'启用' if self.config['multi_room_betting'] else '禁用'}")

    def should_place_bet(self, confidence: float) -> tuple[bool, str]:
        """判断是否应该投注"""

        # 检查置信度阈值
        if self.config['selective_betting'] and confidence < self.config['min_confidence_threshold']:
            return False, f"置信度过低 ({confidence:.3f} < {self.config['min_confidence_threshold']})"

        # 检查连续失败限制
        if self.consecutive_losses >= self.config['max_consecutive_losses']:
            return False, f"连续失败次数过多 ({self.consecutive_losses}次)"

        # 检查日损失限制
        if self.daily_loss >= self.config['max_daily_loss']:
            return False, f"达到日损失限制 ({self.daily_loss:.2f}元)"

        # 检查余额
        min_bet = self.config['base_bet_amount']
        if self.current_balance < min_bet:
            return False, f"余额不足 ({self.current_balance:.2f}元 < {min_bet:.2f}元)"

        # 检查时间段风险
        current_hour = datetime.now().hour
        if 2 <= current_hour <= 5:  # 深夜高风险时段
            if confidence < 0.7:
                return False, f"深夜时段需要更高置信度 ({confidence:.3f} < 0.7)"

        return True, "通过所有检查"

    def calculate_bet_amount(self, confidence: float, room_count: int = 1) -> float:
        """计算投注金额"""

        base_amount = self.config['base_bet_amount']

        if not self.config['dynamic_amount_adjustment']:
            return base_amount

        # 基于置信度调整
        confidence_multiplier = 0.5 + (confidence * 1.0)  # 0.5-1.5倍

        # 基于连败情况调整（马丁格尔策略的保守版本）
        if self.consecutive_losses > 0:
            martingale_multiplier = min(1.5 ** self.consecutive_losses, 3.0)  # 最大3倍
        else:
            martingale_multiplier = 1.0

        # 基于连胜情况调整
        if self.consecutive_wins >= 3:
            win_bonus = 1 + (self.consecutive_wins - 2) * 0.1  # 每连胜1次增加10%
            win_bonus = min(win_bonus, 1.5)  # 最大1.5倍
        else:
            win_bonus = 1.0

        # 基于余额比例调整
        balance_ratio = self.current_balance / self.initial_balance
        balance_multiplier = min(balance_ratio, 1.0)  # 余额减少时降低投注

        # 综合计算
        calculated_amount = (base_amount * confidence_multiplier *
                           martingale_multiplier * win_bonus * balance_multiplier)

        # 多房间投注时平均分配
        if room_count > 1:
            calculated_amount /= room_count

        # 限制在合理范围内
        final_amount = max(0.5, min(calculated_amount, self.config['max_bet_amount']))
        final_amount = min(final_amount, self.current_balance * 0.2)  # 不超过余额的20%

        return round(final_amount, 2)

    def make_betting_decision(self, current_issue: int,
                            room_investment_data: Optional[Dict] = None) -> Dict[str, Any]:
        """制定投注决策"""

        print(f"\n🎯 第{current_issue}期 - 增强多算法投注决策")
        print("=" * 60)

        # 多算法投票预测
        voting_result = self.multi_algorithm_voter.vote_for_prediction(list(self.game_history))

        print(f"🗳️ 算法投票结果:")
        print(f"   推荐避开房间: {voting_result.recommended_room}")
        print(f"   综合置信度: {voting_result.confidence:.3f}")
        print(f"   共识水平: {voting_result.consensus_level:.3f}")

        # 显示各算法详情
        for algo_name, result in voting_result.algorithm_votes.items():
            print(f"   {algo_name}: 房间{result.predicted_room} (置信度{result.confidence:.3f})")

        # 判断是否投注
        should_bet, bet_reason = self.should_place_bet(voting_result.confidence)

        if not should_bet:
            print(f"❌ 跳过投注: {bet_reason}")
            return {
                'should_bet': False,
                'target_rooms': [],
                'bet_amounts': {},
                'confidence': voting_result.confidence,
                'reason': bet_reason,
                'voting_result': voting_result
            }

        # 智能房间选择
        target_rooms = self.smart_room_selector.select_optimal_rooms(
            avoid_room=voting_result.recommended_room,
            confidence=voting_result.confidence,
            room_investment_data=room_investment_data
        )

        # 计算投注金额
        room_count = len(target_rooms) if self.config['multi_room_betting'] else 1
        if not self.config['multi_room_betting']:
            target_rooms = target_rooms[:1]  # 只选择第一个房间

        bet_amount = self.calculate_bet_amount(voting_result.confidence, room_count)
        bet_amounts = {room: bet_amount for room in target_rooms}

        print(f"✅ 投注决策:")
        print(f"   目标房间: {target_rooms}")
        print(f"   投注金额: {bet_amount:.2f}元/房间")
        print(f"   总投注: {sum(bet_amounts.values()):.2f}元")
        print(f"   决策理由: {bet_reason}")

        return {
            'should_bet': True,
            'target_rooms': target_rooms,
            'bet_amounts': bet_amounts,
            'confidence': voting_result.confidence,
            'reason': bet_reason,
            'voting_result': voting_result
        }

    def process_betting_result(self, issue: int, decision: Dict[str, Any],
                             actual_result: int) -> Dict[str, Any]:
        """处理投注结果"""

        if not decision['should_bet']:
            # 未投注的情况
            voting_result = decision['voting_result']
            self.multi_algorithm_voter.update_algorithm_performance(actual_result, voting_result)

            print(f"📊 第{issue}期结果: 未投注 (开奖房间{actual_result})")
            return {
                'issue': issue,
                'won': False,
                'profit': 0.0,
                'decision': decision,
                'actual_result': actual_result
            }

        # 计算投注结果
        target_rooms = decision['target_rooms']
        bet_amounts = decision['bet_amounts']
        won = actual_result in target_rooms
        total_bet = sum(bet_amounts.values())

        if won:
            # 胜利：获得10%收益
            profit = total_bet * 0.1
            self.consecutive_wins += 1
            self.consecutive_losses = 0
        else:
            # 失败：损失全部投注金额
            profit = -total_bet
            self.consecutive_losses += 1
            self.consecutive_wins = 0
            self.daily_loss += total_bet

        # 更新余额和统计
        self.current_balance += profit
        self.total_bets += 1
        if won:
            self.total_wins += 1
        self.total_profit += profit

        # 更新算法性能
        voting_result = decision['voting_result']
        self.multi_algorithm_voter.update_algorithm_performance(actual_result, voting_result)

        # 更新房间性能
        for room in target_rooms:
            self.smart_room_selector.update_room_performance(room, won)

        # 显示结果
        status = "🎉 获胜" if won else "❌ 失败"
        print(f"📊 第{issue}期结果: {status}")
        print(f"   开奖房间: {actual_result}")
        print(f"   投注房间: {target_rooms}")
        print(f"   盈亏: {profit:+.2f}元")
        print(f"   当前余额: {self.current_balance:.2f}元")
        print(f"   连胜/连败: {self.consecutive_wins}/{self.consecutive_losses}")

        return {
            'issue': issue,
            'won': won,
            'profit': profit,
            'decision': decision,
            'actual_result': actual_result,
            'total_bet': total_bet
        }

    def add_game_result(self, result: int):
        """添加游戏结果到历史"""
        self.game_history.append(result)

    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""

        if self.total_bets == 0:
            win_rate = 0.0
        else:
            win_rate = self.total_wins / self.total_bets

        session_duration = datetime.now() - self.session_start_time

        # 算法权重信息
        algorithm_weights = self.multi_algorithm_voter.algorithm_weights.copy()

        # 最近性能
        recent_performance = {}
        for algo_name, history in self.multi_algorithm_voter.performance_history.items():
            if len(history) >= 10:
                recent_perf = sum(list(history)[-10:]) / 10
                recent_performance[algo_name] = recent_perf
            else:
                recent_performance[algo_name] = 0.5

        return {
            'total_bets': self.total_bets,
            'total_wins': self.total_wins,
            'win_rate': win_rate,
            'total_profit': self.total_profit,
            'current_balance': self.current_balance,
            'roi': (self.current_balance - self.initial_balance) / self.initial_balance * 100,
            'consecutive_wins': self.consecutive_wins,
            'consecutive_losses': self.consecutive_losses,
            'daily_loss': self.daily_loss,
            'session_duration': str(session_duration).split('.')[0],
            'algorithm_weights': algorithm_weights,
            'recent_algorithm_performance': recent_performance,
            'game_history_length': len(self.game_history)
        }
