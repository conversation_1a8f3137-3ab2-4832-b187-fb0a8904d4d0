#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实API测试系统 - 连接真实游戏API但只进行模拟投注
用于长期测试增强多算法融合系统的性能
"""

import time
import json
import threading
from datetime import datetime, timed<PERSON><PERSON>
from enhanced_multi_algorithm_betting_system import EnhancedMultiAlgorithmBettingSystem
from api_framework import GameAPIClient, GameMonitor, GameState
from typing import Dict, List, Optional


class RealAPITestClient:
    """真实API测试客户端 - 只获取数据，不实际投注"""
    
    def __init__(self, api_client: GameAPIClient):
        self.api_client = api_client
        self.balance = 100.0  # 模拟余额
        
    def get_balance(self):
        """获取模拟余额"""
        return self.balance
    
    def place_bet(self, room, amount):
        """模拟投注 - 不实际调用API"""
        print(f"🔄 模拟投注: 房间{room}, 金额{amount}元 (不实际投注)")
        
        # 模拟投注成功
        class MockBetResult:
            def __init__(self):
                self.success = True
                self.total_amount = amount
        
        return MockBetResult()
    
    def get_real_game_state(self) -> Optional[GameState]:
        """获取真实游戏状态"""
        return self.api_client.get_game_state()


class RealAPITestSystem:
    """真实API测试系统"""
    
    def __init__(self, api_config: Dict):
        """初始化测试系统"""
        self.api_config = api_config
        self.setup_api_client()
        self.setup_enhanced_system()
        self.setup_monitoring()
        
        # 测试统计
        self.test_stats = {
            'start_time': datetime.now(),
            'total_predictions': 0,
            'correct_predictions': 0,
            'api_calls': 0,
            'api_errors': 0,
            'game_data_history': [],
            'prediction_history': []
        }
        
        self.is_running = False
        self.current_issue = 0
        
    def setup_api_client(self):
        """设置API客户端"""
        print("🔧 设置真实API客户端...")
        
        # 创建真实API客户端
        self.real_api_client = GameAPIClient(
            self.api_config['base_url'],
            self.api_config['headers']
        )
        
        # 创建测试客户端包装器
        self.test_client = RealAPITestClient(self.real_api_client)
        
        print("✅ API客户端设置完成")
    
    def setup_enhanced_system(self):
        """设置增强多算法系统"""
        print("🔧 设置增强多算法融合系统...")
        
        # 配置参数
        config = {
            'base_bet_amount': 2.0,
            'max_bet_amount': 10.0,
            'min_bet_amount': 1.0,
            'max_consecutive_losses': 5,
            'max_daily_loss': 20000.0,  # 使用用户修改的值
            'stop_loss_percentage': 0.3,
            'initial_balance': 100.0
        }
        
        # 创建增强系统
        self.enhanced_system = EnhancedMultiAlgorithmBettingSystem(
            self.test_client, config
        )
        
        print("✅ 增强多算法系统设置完成")
    
    def setup_monitoring(self):
        """设置监控"""
        print("🔧 设置游戏监控...")
        
        self.monitor = GameMonitor(self.real_api_client)
        
        print("✅ 游戏监控设置完成")
    
    def test_api_connection(self) -> bool:
        """测试API连接"""
        print("\n🔍 测试API连接...")
        
        try:
            state = self.test_client.get_real_game_state()
            self.test_stats['api_calls'] += 1
            
            if state:
                print(f"✅ API连接成功!")
                print(f"   当前期号: {state.issue}")
                print(f"   游戏状态: {state.state}")
                print(f"   倒计时: {state.countdown}秒")
                print(f"   房间数据: {len(state.room_stats)}个房间")
                return True
            else:
                print("⚠️ API连接成功但未获取到数据")
                return False
                
        except Exception as e:
            print(f"❌ API连接失败: {e}")
            self.test_stats['api_errors'] += 1
            return False
    
    def convert_room_stats_to_investment_data(self, room_stats: Dict) -> Dict:
        """将API房间数据转换为投资数据格式"""
        investment_data = {}
        
        for room_num, stats in room_stats.items():
            investment_data[room_num] = {
                'amount': stats.get('total_medal', 0),
                'users': stats.get('user_count', 0),
                'props': stats.get('total_stroke', 0)
            }
        
        return investment_data
    
    def process_game_result(self, state: GameState):
        """处理游戏结果"""
        if state.state == 2 and state.kill_number > 0:  # 已开奖
            # 记录真实开奖结果
            self.test_stats['game_data_history'].append({
                'issue': state.issue,
                'kill_number': state.kill_number,
                'timestamp': state.timestamp,
                'room_stats': state.room_stats
            })
            
            # 处理增强系统的结果
            if hasattr(self, 'last_prediction'):
                self.enhanced_system.process_enhanced_result(
                    state.issue, 
                    state.kill_number
                )
                
                # 统计预测准确性
                if self.last_prediction['room'] != state.kill_number:
                    self.test_stats['correct_predictions'] += 1
                
                print(f"🎯 期号{state.issue}: 预测房间{self.last_prediction['room']}, "
                      f"实际房间{state.kill_number}, "
                      f"结果: {'✅获胜' if self.last_prediction['room'] != state.kill_number else '❌失败'}")
    
    def make_prediction_and_test(self, state: GameState):
        """进行预测和测试"""
        if state.state == 1:  # 等待开奖状态
            # 转换房间数据格式
            investment_data = self.convert_room_stats_to_investment_data(state.room_stats)
            
            # 使用增强系统进行预测
            bet_info = self.enhanced_system.execute_enhanced_bet(
                state.issue, 
                investment_data
            )
            
            if bet_info:
                self.last_prediction = bet_info
                self.test_stats['total_predictions'] += 1
                
                # 记录预测历史
                self.test_stats['prediction_history'].append({
                    'issue': state.issue,
                    'predicted_room': bet_info['room'],
                    'amount': bet_info['amount'],
                    'timestamp': datetime.now().isoformat(),
                    'algorithms_used': bet_info.get('algorithms_used', [])
                })
                
                print(f"🔮 期号{state.issue}预测: 房间{bet_info['room']}, "
                      f"金额{bet_info['amount']}元")
    
    def print_test_statistics(self):
        """打印测试统计"""
        runtime = datetime.now() - self.test_stats['start_time']
        accuracy = (self.test_stats['correct_predictions'] / 
                   max(1, self.test_stats['total_predictions'])) * 100
        
        print(f"\n📊 实时测试统计:")
        print(f"   运行时间: {runtime}")
        print(f"   总预测次数: {self.test_stats['total_predictions']}")
        print(f"   正确预测: {self.test_stats['correct_predictions']}")
        print(f"   预测准确率: {accuracy:.1f}%")
        print(f"   API调用次数: {self.test_stats['api_calls']}")
        print(f"   API错误次数: {self.test_stats['api_errors']}")
        
        # 显示增强系统统计
        enhanced_stats = self.enhanced_system.get_enhanced_statistics()
        print(f"   系统胜率: {enhanced_stats['win_rate']*100:.1f}%")
        print(f"   模拟余额: {enhanced_stats['current_balance']:.2f}元")
        print(f"   模拟收益: {enhanced_stats['total_profit']:.2f}元")
    
    def save_test_data(self):
        """保存测试数据"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"real_api_test_data_{timestamp}.json"
        
        # 获取增强系统统计
        enhanced_stats = self.enhanced_system.get_enhanced_statistics()
        
        test_data = {
            'test_info': {
                'start_time': self.test_stats['start_time'].isoformat(),
                'end_time': datetime.now().isoformat(),
                'total_runtime_seconds': (datetime.now() - self.test_stats['start_time']).total_seconds()
            },
            'api_stats': {
                'total_calls': self.test_stats['api_calls'],
                'total_errors': self.test_stats['api_errors'],
                'error_rate': self.test_stats['api_errors'] / max(1, self.test_stats['api_calls'])
            },
            'prediction_stats': {
                'total_predictions': self.test_stats['total_predictions'],
                'correct_predictions': self.test_stats['correct_predictions'],
                'accuracy': (self.test_stats['correct_predictions'] / 
                           max(1, self.test_stats['total_predictions'])) * 100
            },
            'enhanced_system_stats': enhanced_stats,
            'algorithm_performance': self.enhanced_system.get_algorithm_performance_summary(),
            'game_history': self.test_stats['game_data_history'],
            'prediction_history': self.test_stats['prediction_history']
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 测试数据已保存到: {filename}")
        return filename

    def start_long_term_test(self, duration_hours: int = 24):
        """开始长期测试"""
        print(f"\n🚀 开始长期API测试 (持续{duration_hours}小时)")
        print("=" * 80)

        # 测试API连接
        if not self.test_api_connection():
            print("❌ API连接失败，无法开始测试")
            return

        self.is_running = True
        end_time = datetime.now() + timedelta(hours=duration_hours)
        last_stats_time = datetime.now()
        last_save_time = datetime.now()

        print(f"📅 测试开始时间: {datetime.now()}")
        print(f"📅 预计结束时间: {end_time}")
        print(f"🔄 开始监控游戏状态...")

        try:
            while self.is_running and datetime.now() < end_time:
                try:
                    # 获取游戏状态
                    state = self.test_client.get_real_game_state()
                    self.test_stats['api_calls'] += 1

                    if state:
                        # 处理新期号
                        if state.issue != self.current_issue:
                            if self.current_issue > 0:
                                # 处理上一期的结果
                                self.process_game_result(state)

                            self.current_issue = state.issue

                            # 进行新的预测
                            self.make_prediction_and_test(state)

                        # 每10分钟打印一次统计
                        if datetime.now() - last_stats_time > timedelta(minutes=10):
                            self.print_test_statistics()
                            last_stats_time = datetime.now()

                        # 每小时保存一次数据
                        if datetime.now() - last_save_time > timedelta(hours=1):
                            self.save_test_data()
                            last_save_time = datetime.now()

                    else:
                        self.test_stats['api_errors'] += 1
                        print("⚠️ 未获取到游戏状态数据")

                except Exception as e:
                    self.test_stats['api_errors'] += 1
                    print(f"❌ 处理游戏状态异常: {e}")

                # 等待30秒后继续
                time.sleep(30)

        except KeyboardInterrupt:
            print("\n⏹️ 用户中断测试")

        finally:
            self.is_running = False
            print(f"\n🏁 测试结束")

            # 打印最终统计
            self.print_test_statistics()

            # 保存最终数据
            final_file = self.save_test_data()

            print(f"\n📋 测试总结:")
            print(f"   最终数据文件: {final_file}")
            print(f"   建议: 分析测试数据以优化算法参数")

    def stop_test(self):
        """停止测试"""
        print("⏹️ 正在停止测试...")
        self.is_running = False


def load_config_from_file(config_file: str = "real_api_config.json") -> Dict:
    """从配置文件加载配置"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)

        # 动态更新时间戳
        config['api']['headers']['ts'] = str(int(time.time() * 1000))

        print(f"✅ 配置文件加载成功: {config_file}")
        return config
    except FileNotFoundError:
        print(f"⚠️ 配置文件未找到: {config_file}，使用默认配置")
        return create_default_config()
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}，使用默认配置")
        return create_default_config()


def create_default_config():
    """创建默认配置"""
    return {
        'api': {
            'base_url': 'https://fks-api.lucklyworld.com',
            'headers': {
                'User-Agent': 'com.caike.union/5.2.2-official Dalvik/2.1.0 (Linux; U; Android 9; OPPO R9s Build/PQ3A.190605.04081832)',
                'packageId': 'com.caike.union',
                'version': '5.2.2',
                'channel': 'official',
                'androidId': 'e21953ffb86fa7a8',
                'userId': '8607652',  # 🔴 需要替换为您的实际用户ID
                'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiIiLCJhdWQiOiIiLCJqdGkiOiI4NjA3NjUyIiwiaWF0IjoxNzUzNDM1MzIxLCJuYmYiOjE3NTM0MzUzMjEsImV4cCI6MTc1NjAyNzMyMSwidHlwZSI6InYxMWFwcCIsImF1dGhlbnRpY2F0aW9uU3RhdGUiOnRydWV9.2BTy855tgkF57kscdo1NTHbsygZUBn93W1vM5vmrXAo',  # 🔴 需要替换为您的实际token
                'IMEI': '',
                'ts': str(int(time.time() * 1000)),
                'sign': 'd0940ee13b368d3d8b8b60c7e56614b4ef02732d3214658394bb0ea13c31e277',  # 🔴 需要替换为实际签名
                'Content-Type': 'application/x-www-form-urlencoded',
                'Host': 'fks-api.lucklyworld.com',
                'Connection': 'Keep-Alive',
                'Accept-Encoding': 'gzip'
            }
        },
        'test_settings': {
            'default_duration_hours': 24,
            'api_call_interval_seconds': 30,
            'stats_print_interval_minutes': 10,
            'data_save_interval_hours': 1,
            'max_api_errors': 100,
            'retry_on_error': True,
            'retry_delay_seconds': 5
        },
        'algorithm_config': {
            'base_bet_amount': 2.0,
            'max_bet_amount': 10.0,
            'min_bet_amount': 1.0,
            'max_consecutive_losses': 5,
            'max_daily_loss': 20000.0,
            'stop_loss_percentage': 0.3,
            'initial_balance': 100.0
        }
    }


def main():
    """主程序"""
    print("🔬 真实API测试系统 - 增强多算法融合版本")
    print("=" * 80)
    print("📋 功能说明:")
    print("   ✅ 连接真实游戏API获取数据")
    print("   ✅ 使用增强多算法融合系统进行预测")
    print("   ✅ 只进行模拟投注，不实际花费")
    print("   ✅ 长期测试算法性能")
    print("   ✅ 自动保存测试数据和统计")
    print("   ✅ 支持配置文件管理")

    # 加载配置
    config = load_config_from_file()

    # 显示配置信息
    print(f"\n📋 当前配置:")
    print(f"   API地址: {config['api']['base_url']}")
    print(f"   用户ID: {config['api']['headers']['userId']}")
    print(f"   默认测试时长: {config['test_settings']['default_duration_hours']}小时")
    print(f"   API调用间隔: {config['test_settings']['api_call_interval_seconds']}秒")

    # 创建测试系统
    test_system = RealAPITestSystem(config['api'])

    # 询问测试时长
    default_hours = config['test_settings']['default_duration_hours']
    print(f"\n⏰ 请选择测试时长:")
    print(f"   1. 1小时测试")
    print(f"   2. 6小时测试")
    print(f"   3. 24小时测试")
    print(f"   4. {default_hours}小时测试 (配置默认)")
    print(f"   5. 自定义时长")

    choice = input("请输入选择 (1-5): ").strip()

    duration_hours = 1
    if choice == '2':
        duration_hours = 6
    elif choice == '3':
        duration_hours = 24
    elif choice == '4':
        duration_hours = default_hours
    elif choice == '5':
        try:
            duration_hours = int(input("请输入测试小时数: "))
        except ValueError:
            duration_hours = default_hours

    print(f"\n🎯 将进行{duration_hours}小时的长期测试")
    print(f"📊 预计数据点: ~{duration_hours * 2}个 (每30分钟一期)")
    print(f"💾 数据保存间隔: 每{config['test_settings']['data_save_interval_hours']}小时")

    # 确认开始
    confirm = input("\n确认开始测试? (y/n): ").lower().strip()
    if confirm in ['y', 'yes', '是']:
        print(f"\n🚀 开始长期API测试...")
        print(f"💡 提示: 按 Ctrl+C 可随时停止测试")
        try:
            test_system.start_long_term_test(duration_hours)
        except KeyboardInterrupt:
            print("\n⏹️ 测试被用户中断")
        finally:
            print("🔚 测试系统已关闭")
    else:
        print("❌ 测试已取消")


def quick_test():
    """快速测试API连接"""
    print("🔍 快速API连接测试")
    print("=" * 40)

    config = load_config_from_file()
    test_system = RealAPITestSystem(config['api'])

    if test_system.test_api_connection():
        print("✅ API连接正常，可以开始长期测试")
    else:
        print("❌ API连接失败，请检查配置")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        quick_test()
    else:
        main()


if __name__ == "__main__":
    main()
